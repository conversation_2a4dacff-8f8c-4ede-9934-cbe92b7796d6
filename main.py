import uvicorn
from fastapi import FastAPI, Depends
import asyncio
from redis import asyncio as aioredis
import json
import os
from app.chat_manager import chat
from config.get_config import config
from app.database import engine, get_db
from app.models import Base
from sqlalchemy.orm import Session

# redis配置
url = (config['db_redis']['host'], config['db_redis']['port'])
host = config['db_redis']['host']
db = config['db_redis']['db']
timeout = config['db_redis']['timeout']
password = config['db_redis']['password']
port = config['db_redis']['port']

# 初始化数据库
Base.metadata.create_all(bind=engine)

# 初始化app
app = FastAPI(title="Ws Chat", description="WebSocket聊天服务", version="1.0.0")

# 导入API模块
from app.api import trtc

# 只保留WebSocket相关路由
app.include_router(chat.app, prefix='/api/chat', tags=['Chat'])
app.include_router(trtc.router, prefix='/api/trtc', tags=['trtc'])

# 添加CORS支持
from fastapi.middleware.cors import CORSMiddleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 保存后台任务的引用，以便在关闭时取消
background_tasks = []

@app.on_event('startup')
async def on_startup():
    print(f"订阅初始化:{os.getpid()}")

    # 启动WebSocket心跳检查任务
    try:
        print("启动WebSocket心跳检查任务...")
        await chat.cm.start_heartbeat_check()
        print("WebSocket心跳检查任务已启动")
    except Exception as e:
        print(f"启动WebSocket心跳检查任务时出错: {e}")

    # 执行消息订阅机制
    loop = asyncio.get_event_loop()
    task = loop.create_task(register_pubsub())
    # 保存任务引用
    background_tasks.append(task)

@app.on_event('shutdown')
async def on_shutdown():
    # 停止心跳检查任务
    try:
        await chat.cm.stop_heartbeat_check()
    except Exception as e:
        print(f"停止WebSocket心跳检查任务时出错: {e}")

    # 断开所有WebSocket连接
    try:
        for user_id in list(chat.cm.websocket_connections.keys()):
            try:
                await chat.cm.disconnect(user_id)
            except Exception as e:
                print(f"断开用户 {user_id} 连接时出错: {e}")
    except Exception as e:
        print(f"断开WebSocket连接时出错: {e}")

    # 关闭Redis连接池
    try:
        from utils.redis_util import close_redis_pool
        await close_redis_pool()
        print("Redis连接池已关闭")
    except Exception as e:
        print(f"关闭Redis连接池时出错: {e}")

    # 取消所有后台任务
    for task in background_tasks:
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

# 保留Redis发布订阅功能
async def register_pubsub():
    """注册Redis PubSub并处理消息"""
    from utils.redis_util import get_redis_pool

    # 创建Redis连接池
    pool = None
    psub = None

    try:
        # 使用统一的Redis连接池
        pool = get_redis_pool()
        redis_client = aioredis.Redis(connection_pool=pool)

        # 创建发布订阅对象
        psub = redis_client.pubsub()

        # 订阅聊天频道
        await psub.subscribe("chat")

        # 处理消息
        async for message in psub.listen():
            if message["type"] == "message":
                try:
                    # 解析消息
                    data = json.loads(message["data"])
                    # 处理消息
                    await chat.cm.handle_redis_message(data)
                except Exception as e:
                    print(f"处理Redis消息时出错: {e}")
    except Exception as e:
        print(f"Redis订阅出错: {e}")
    finally:
        # 清理资源
        if psub:
            await psub.unsubscribe("chat")
            await psub.close()
        # 注意：不要关闭连接池，因为其他地方还在使用

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=298)
