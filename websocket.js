/**
 * WebSocket通信模块
 */
import { updateUIState } from './dom.js';
import { addOfflineMessage, sendAllOfflineMessages } from './offline-queue.js';
import { getCurrentUser, checkAndSyncUserState, clearUserSession } from './user.js';

// 调试模式开关，设置为true时会打印更多日志
window.DEBUG_MODE = false;

let socket = null;
let messageHandlers = {};
let reconnectAttempts = 0;
let maxReconnectAttempts = 10; // 增加最大重连次数，从5次增加到10次
let reconnectInterval = 3000; // 3秒
let userId = null;
let onOpenCallback = null;
let heartbeatInterval = null;
let heartbeatTimer = null;
let connectionCheckTimer = null; // 连接状态检查定时器
let isClosing = false; // 标记连接是否正在关闭
let pendingConnection = false; // 标记是否有待处理的连接请求
let heartbeatResponseHandler = null; // 心跳响应事件处理器
let lastUserActivity = Date.now(); // 最后用户活动时间
let isPageVisible = true; // 页面可见性状态

/**
 * 获取当前WebSocket连接
 * @returns {WebSocket|null} WebSocket连接对象
 */
export function getSocket() {
    return socket;
}

/**
 * 连接WebSocket
 * @param {string} userIdParam - 用户ID
 * @param {Function} onOpenCallbackParam - 连接打开时的回调
 * @param {Object} handlers - 消息类型处理器对象
 * @returns {Promise<WebSocket>} WebSocket连接对象
 */
export function connectWebSocket(userIdParam, onOpenCallbackParam, handlers = {}) {
    // 保存参数以便重连
    userId = userIdParam;
    onOpenCallback = onOpenCallbackParam;
    reconnectAttempts = 0;

    return new Promise((resolve, reject) => {
        // 保存消息处理器
        messageHandlers = handlers;

        // 创建WebSocket连接
        createWebSocketConnection(resolve, reject);
    });
}

/**
 * 创建WebSocket连接
 * @param {Function} resolve - Promise解决函数
 * @param {Function} reject - Promise拒绝函数
 */
function createWebSocketConnection(resolve, reject) {
    try {
        // 如果已经有待处理的连接请求，不要重复创建
        if (pendingConnection) {
            console.log('已有待处理的连接请求，忽略此次连接尝试');
            return;
        }

        // 更新连接状态为连接中
        updateConnectionState('connecting');

        // 如果已经有连接，先关闭并等待完全关闭
        if (socket && socket.readyState !== WebSocket.CLOSED) {
            // 更新连接状态为关闭中
            updateConnectionState('closing');

            // 设置关闭超时，防止关闭操作卡住
            const closeTimeout = setTimeout(() => {
                console.log('关闭连接超时，强制继续');
                // 强制清理资源
                cleanupWebSocketResources();
                proceedToConnect(resolve, reject);
            }, 2000);

            // 监听关闭事件
            const onCloseHandler = () => {
                clearTimeout(closeTimeout);
                socket.removeEventListener('close', onCloseHandler);
                proceedToConnect(resolve, reject);
            };

            socket.addEventListener('close', onCloseHandler);

            // 停止心跳
            stopHeartbeat();

            // 关闭连接
            try {
                socket.close();
            } catch (err) {
                console.error('关闭WebSocket连接时出错:', err);
                // 出错时也要清理资源
                cleanupWebSocketResources();
                proceedToConnect(resolve, reject);
            }
            return;
        }

        proceedToConnect(resolve, reject);
    } catch (error) {
        // 更新连接状态为错误
        updateConnectionState('error', { error });
        console.error('创建WebSocket连接时出错:', error);
        if (reject) reject(error);
    }
}

/**
 * 继续连接过程
 * @param {Function} resolve - Promise解决函数
 * @param {Function} reject - Promise拒绝函数
 */
function proceedToConnect(resolve, reject) {
    try {
        // 更新连接状态
        updateConnectionState('connecting');

        // 创建WebSocket连接
        // 根据当前协议自动选择WebSocket协议
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // socket = new WebSocket(`${protocol}//${window.location.host}/api/chat/connect_chat?user_code=${userId}`);
        // socket = new WebSocket(`ws://*************:8000/api/chat/connect_chat?user_code=${userId}`);
        socket = new WebSocket(`wss://chats.mfcad.com/api/chat/connect_chat?user_code=${userId}`);
        // socket = new WebSocket(`wss://chat.mfcad.com/api/chat/connect_chat?user_code=${userId}`);
        socket.onopen = () => {
            console.log('WebSocket连接成功');
            updateUIState(true);
            reconnectAttempts = 0; // 重置重连计数

            // 更新连接状态为已连接
            updateConnectionState('connected');

            // 更新最后用户活动时间
            lastUserActivity = Date.now();

            // 启动心跳检测，根据页面可见性设置不同的心跳间隔
            const interval = isPageVisible ? 30000 : 60000;
            startHeartbeat(interval);

            // 启动连接状态定期检查
            startConnectionCheck();

            // 确保用户信息在UI中正确显示
            const currentUser = getCurrentUser();
            if (currentUser && document.getElementById('username')) {
                document.getElementById('username').textContent = currentUser.username;
            }

            // 发送所有离线消息
            sendAllOfflineMessages((message) => {
                return sendMessage(message);
            }).then(result => {
                if (result.count > 0) {
                    console.log(`离线消息发送结果: 总数=${result.count}, 成功=${result.successCount}, 失败=${result.failCount}`);

                    // 触发离线消息发送完成事件
                    document.dispatchEvent(new CustomEvent('ws-offline-messages-sent', {
                        detail: result
                    }));
                }
            }).catch(error => {
                console.error('发送离线消息时出错:', error);
            });

            if (typeof onOpenCallback === 'function') {
                onOpenCallback();
            }

            if (resolve) resolve(socket);
        };

        socket.onmessage = handleWebSocketMessage;

        socket.onclose = (event) => {
            console.log(`WebSocket连接关闭，代码: ${event.code}, 原因: ${event.reason || '未知原因'}`);

            // 清理WebSocket资源
            cleanupWebSocketResources();

            // 更新连接状态为已关闭
            updateConnectionState('closed', {
                code: event.code,
                reason: event.reason,
                timestamp: new Date().toISOString()
            });

            // 如果是主动关闭连接，不进行重连
            if (isClosing) {
                console.log('连接已主动关闭，不进行重连');
                return;
            }

            // 如果用户已登录且重连次数未超过限制，尝试重连
            if (userId && reconnectAttempts < maxReconnectAttempts) {
                reconnectAttempts++;

                // 使用指数退避算法计算延迟时间
                // 基础延迟为3秒，每次重试增加1.5倍
                const baseDelay = reconnectInterval * Math.pow(1.5, reconnectAttempts - 1);

                // 添加随机抖动，避免连接风暴（最大30%的随机抖动）
                const jitter = baseDelay * 0.3 * Math.random();

                // 计算最终延迟时间，并设置上限为60秒
                const delay = Math.min(baseDelay + jitter, 60000);

                console.log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts}), 延迟: ${Math.round(delay)}ms`);

                // 更新会话时间戳，保持会话活跃
                localStorage.setItem('session_timestamp', new Date().getTime().toString());

                // 更新最后用户活动时间
                lastUserActivity = Date.now();

                setTimeout(() => {
                    // 重连前检查用户状态
                    checkAndSyncUserState();

                    // 检查页面可见性，如果页面不可见且已经尝试多次，延长重试间隔
                    if (!isPageVisible && reconnectAttempts > 3) {
                        console.log('页面不可见，延长重试间隔');
                        // 如果页面不可见且已经尝试了多次，增加额外延迟
                        setTimeout(() => {
                            // 传递resolve和reject参数，确保Promise处理正确
                            createWebSocketConnection(resolve, reject);
                        }, 5000); // 额外延迟5秒
                    } else {
                        // 传递resolve和reject参数，确保Promise处理正确
                        createWebSocketConnection(resolve, reject);
                    }
                }, delay);
            } else {
                updateUIState(false);
                socket = null;

                // 如果超过重连次数，显示提示
                if (reconnectAttempts >= maxReconnectAttempts) {
                    console.error(`已达到最大重连次数 (${maxReconnectAttempts})，停止自动重连`);

                    // 显示重连按钮，而不是自动刷新页面
                    const reconnectMessage = '连接已断开，是否重新连接？';
                    if (confirm(reconnectMessage)) {
                        // 用户选择重连，重置重连计数并尝试重连
                        console.log('用户选择重新连接，重置重连计数');
                        reconnectAttempts = 0;
                        pendingConnection = false; // 重置连接状态标记
                        createWebSocketConnection(resolve, reject);
                    } else {
                        // 用户选择不重连，清除会话
                        console.log('用户选择不重新连接，清除会话');
                        clearUserSession();
                        // 刷新页面
                        window.location.reload();
                    }
                }
            }
        };

        socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            updateUIState(false);

            // 停止心跳检测
            stopHeartbeat();

            // 更新连接状态为错误，添加更多上下文信息
            updateConnectionState('error', {
                error,
                timestamp: new Date().toISOString(),
                reconnectAttempt: reconnectAttempts,
                userId: userId
            });

            // 如果是初次连接失败，清除会话
            if (reconnectAttempts === 0) {
                clearUserSession();
                // 使用更友好的错误提示，不直接刷新页面
                const errorMessage = '连接服务器失败，请检查网络后重试';
                console.error(errorMessage);

                // 触发连接错误事件，让UI层处理提示
                document.dispatchEvent(new CustomEvent('ws-connection-error', {
                    detail: { message: errorMessage }
                }));

                // 不立即刷新页面，给用户一个选择的机会
                if (confirm(errorMessage + '。是否刷新页面？')) {
                    window.location.reload();
                }
            }

            if (reject) reject(error);
        };
    } catch (error) {
        // 更新连接状态为错误
        updateConnectionState('error', { error });
        console.error('创建WebSocket连接时出错:', error);
        if (reject) reject(error);
    }
}

/**
 * 启动心跳检测
 * 定期发送心跳包，检测连接状态
 * @param {number} [interval=30000] - 心跳间隔，默认30秒
 */
function startHeartbeat(interval = 30000) {
    // 停止可能存在的心跳
    stopHeartbeat();

    // 设置心跳间隔
    heartbeatInterval = interval;

    // 上次收到心跳响应的时间
    let lastHeartbeatResponse = Date.now();

    // 连续失败次数计数
    let consecutiveFailures = 0;

    // 最大连续失败次数，超过此值才关闭连接
    const maxConsecutiveFailures = 3;

    // 心跳超时时间（3个心跳周期）
    const heartbeatTimeout = heartbeatInterval * 3;

    // 添加心跳响应处理器
    heartbeatResponseHandler = (event) => {
        const now = Date.now();
        lastHeartbeatResponse = now;

        // 重置连续失败计数
        consecutiveFailures = 0;

        // 如果事件包含详细信息，计算往返时间
        if (event.detail) {
            const { clientTimestamp } = event.detail;
            if (clientTimestamp) {
                const roundTripTime = now - clientTimestamp;
                // 记录往返时间，可用于网络质量监控
                if (window.DEBUG_MODE || roundTripTime > 1000) { // 如果往返时间超过1秒，总是记录
                    console.log(`心跳往返时间: ${roundTripTime}ms`);
                }

                // 如果往返时间异常长，记录警告
                if (roundTripTime > 3000) {
                    console.warn(`心跳往返时间异常长: ${roundTripTime}ms，可能存在网络问题`);

                    // 触发网络质量事件
                    document.dispatchEvent(new CustomEvent('ws-network-quality', {
                        detail: {
                            quality: 'poor',
                            roundTripTime,
                            timestamp: new Date().toISOString()
                        }
                    }));
                }
            }
        }

        if (window.DEBUG_MODE) {
            console.log('更新心跳响应时间:', new Date(lastHeartbeatResponse).toISOString());
        }
    };

    window.addEventListener('heartbeat_response', heartbeatResponseHandler);

    // 启动心跳定时器
    heartbeatTimer = setInterval(() => {
        if (socket && socket.readyState === WebSocket.OPEN) {
            // 检查是否超时
            const now = Date.now();
            const timeSinceLastResponse = now - lastHeartbeatResponse;

            // 分级处理心跳超时
            if (timeSinceLastResponse > heartbeatTimeout) {
                // 增加连续失败计数
                consecutiveFailures++;

                console.warn(`心跳超时 (${Math.round(timeSinceLastResponse / 1000)}秒 > ${heartbeatTimeout/1000}秒)，连续失败次数: ${consecutiveFailures}/${maxConsecutiveFailures}`);

                // 只有在连续失败次数超过阈值时才关闭连接
                if (consecutiveFailures >= maxConsecutiveFailures) {
                    console.error(`心跳连续失败${consecutiveFailures}次，关闭连接并重连`);
                    stopHeartbeat();

                    // 更新连接状态为错误
                    updateConnectionState('error', {
                        reason: 'heartbeat_timeout',
                        lastResponse: new Date(lastHeartbeatResponse).toISOString(),
                        timeSinceLastResponse: Math.round(timeSinceLastResponse / 1000),
                        timeout: heartbeatTimeout / 1000,
                        consecutiveFailures
                    });

                    // 清理资源并关闭连接
                    cleanupWebSocketResources();

                    // 触发心跳超时事件，让UI层可以处理
                    document.dispatchEvent(new CustomEvent('ws-heartbeat-timeout', {
                        detail: {
                            lastResponse: new Date(lastHeartbeatResponse).toISOString(),
                            timeSinceLastResponse: Math.round(timeSinceLastResponse / 1000),
                            consecutiveFailures
                        }
                    }));

                    // 尝试重新连接
                    if (userId) {
                        console.log('心跳超时后尝试重新连接');
                        setTimeout(() => {
                            // 创建新的Promise来处理重连
                            new Promise((innerResolve, innerReject) => {
                                createWebSocketConnection(innerResolve, innerReject);
                            }).catch(err => {
                                console.error('心跳超时后重连失败:', err);
                            });
                        }, 1000); // 延迟1秒后重连
                    }

                    return;
                }

                // 尝试发送恢复心跳包
                try {
                    socket.send(JSON.stringify({
                        type: 'heartbeat',
                        timestamp: now,
                        is_recovery: true,
                        consecutive_failures: consecutiveFailures
                    }));
                    console.log(`发送恢复心跳包 (尝试 ${consecutiveFailures}/${maxConsecutiveFailures})`);
                } catch (error) {
                    console.error('发送恢复心跳包失败:', error);
                }
            } else if (timeSinceLastResponse > heartbeatTimeout * 0.7) {
                // 接近超时，记录警告但继续发送心跳
                console.warn(`心跳接近超时 (${Math.round(timeSinceLastResponse / 1000)}秒 > ${Math.round(heartbeatTimeout * 0.7 / 1000)}秒)，继续尝试`);

                // 尝试发送额外的心跳包
                try {
                    socket.send(JSON.stringify({
                        type: 'heartbeat',
                        timestamp: now,
                        is_recovery: true  // 标记为恢复心跳
                    }));
                    console.log('发送恢复心跳包');
                } catch (error) {
                    console.error('发送恢复心跳包失败:', error);
                }
            }

            // 发送心跳包
            try {
                socket.send(JSON.stringify({ type: 'heartbeat', timestamp: now }));
                // 只在调试模式下打印心跳日志
                if (window.DEBUG_MODE) {
                    console.log('发送心跳包:', new Date(now).toISOString());
                }
            } catch (error) {
                console.error('发送心跳包失败:', error);
                // 增加连续失败计数
                consecutiveFailures++;

                // 只有在连续失败次数超过阈值时才关闭连接
                if (consecutiveFailures >= maxConsecutiveFailures) {
                    console.error(`发送心跳包连续失败${consecutiveFailures}次，关闭连接`);
                    stopHeartbeat();

                    // 更新连接状态为错误
                    updateConnectionState('error', {
                        reason: 'heartbeat_send_failed',
                        error,
                        consecutiveFailures
                    });

                    // 清理资源
                    cleanupWebSocketResources();

                    // 触发心跳发送失败事件
                    document.dispatchEvent(new CustomEvent('ws-heartbeat-send-failed', {
                        detail: {
                            error: error.message || '发送失败',
                            consecutiveFailures,
                            timestamp: new Date().toISOString()
                        }
                    }));

                    // 尝试重新连接
                    if (userId) {
                        console.log('心跳发送失败后尝试重新连接');
                        setTimeout(() => {
                            // 创建新的Promise来处理重连
                            new Promise((innerResolve, innerReject) => {
                                createWebSocketConnection(innerResolve, innerReject);
                            }).catch(err => {
                                console.error('心跳发送失败后重连失败:', err);
                            });
                        }, 1000); // 延迟1秒后重连
                    }
                }
            }
        } else {
            // 连接已关闭，停止心跳
            stopHeartbeat();
        }
    }, heartbeatInterval);
}

/**
 * 停止心跳检测
 */
function stopHeartbeat() {
    // 清除定时器
    if (heartbeatTimer) {
        clearInterval(heartbeatTimer);
        heartbeatTimer = null;
    }

    // 移除事件监听器
    if (heartbeatResponseHandler) {
        window.removeEventListener('heartbeat_response', heartbeatResponseHandler);
        heartbeatResponseHandler = null;
    }
}

/**
 * 更新用户活动时间
 * 用于跟踪用户最后活动时间，优化心跳策略
 */
export function updateUserActivity() {
    lastUserActivity = Date.now();

    // 如果连接存在但处于错误或关闭状态，尝试重新连接
    if (userId && socket && (socket.readyState === WebSocket.CLOSED || socket.readyState === WebSocket.CLOSING)) {
        console.log('检测到用户活动，连接已关闭，尝试重新连接');
        reconnectWebSocket();
    }
}

/**
 * 设置页面可见性状态
 * @param {boolean} visible - 页面是否可见
 */
export function setPageVisibility(visible) {
    const previousState = isPageVisible;
    isPageVisible = visible;

    // 如果页面从不可见变为可见，检查连接状态
    if (!previousState && visible) {
        console.log('页面变为可见，检查WebSocket连接状态');
        checkConnectionStatus();
    }

    // 根据页面可见性调整心跳频率
    adjustHeartbeatInterval();
}

/**
 * 检查连接状态并在需要时重新连接
 */
function checkConnectionStatus() {
    // 如果没有用户ID，不需要连接
    if (!userId) return;

    // 如果连接不存在或已关闭，尝试重新连接
    if (!socket || socket.readyState === WebSocket.CLOSED) {
        console.log('连接检查：连接不存在或已关闭，尝试重新连接');
        reconnectWebSocket();
    }
    // 如果连接正在关闭，等待关闭完成后重新连接
    else if (socket.readyState === WebSocket.CLOSING) {
        console.log('连接检查：连接正在关闭，等待关闭完成后重新连接');
        setTimeout(checkConnectionStatus, 1000);
    }
    // 如果连接存在但处于错误状态，重置连接
    else if (socket.readyState === WebSocket.OPEN) {
        // 发送一个心跳包来验证连接是否真的有效
        try {
            sendMessage({ type: 'heartbeat', timestamp: Date.now(), is_check: true });
            console.log('连接检查：连接存在且打开，已发送心跳包验证连接');
        } catch (error) {
            console.error('连接检查：发送心跳包失败，重置连接', error);
            cleanupWebSocketResources();
            reconnectWebSocket();
        }
    }
}

/**
 * 根据页面可见性和用户活动调整心跳间隔
 */
function adjustHeartbeatInterval() {
    // 如果心跳定时器不存在，不需要调整
    if (!heartbeatTimer) return;

    // 停止当前心跳
    stopHeartbeat();

    // 根据页面可见性设置不同的心跳间隔
    // 页面可见时，使用正常间隔（30秒）
    // 页面不可见时，使用较长间隔（60秒），但不完全停止心跳
    const interval = isPageVisible ? 30000 : 60000;

    // 重新启动心跳
    startHeartbeat(interval);

    console.log(`已调整心跳间隔: ${interval/1000}秒 (页面${isPageVisible ? '可见' : '不可见'})`);
}

/**
 * 尝试重新连接WebSocket
 */
function reconnectWebSocket() {
    // 如果已经有待处理的连接，不要重复连接
    if (pendingConnection) {
        console.log('已有待处理的连接请求，忽略此次重连');
        return;
    }

    // 重置重连尝试次数，给予新的重连机会
    reconnectAttempts = 0;

    // 创建新的Promise来处理重连
    new Promise((resolve, reject) => {
        createWebSocketConnection(resolve, reject);
    }).catch(err => {
        console.error('重连失败:', err);
    });
}

/**
 * 启动连接状态定期检查
 */
function startConnectionCheck() {
    // 停止可能存在的检查
    stopConnectionCheck();

    // 每2分钟检查一次连接状态
    connectionCheckTimer = setInterval(() => {
        // 只有在页面可见且用户最近有活动时才主动检查
        const now = Date.now();
        const userInactive = (now - lastUserActivity) > 5 * 60 * 1000; // 5分钟无活动

        if (isPageVisible && !userInactive) {
            checkConnectionStatus();
        }
    }, 2 * 60 * 1000); // 2分钟
}

/**
 * 停止连接状态检查
 */
function stopConnectionCheck() {
    if (connectionCheckTimer) {
        clearInterval(connectionCheckTimer);
        connectionCheckTimer = null;
    }
}

/**
 * 清理WebSocket资源
 * 确保所有资源都被正确释放
 */
function cleanupWebSocketResources() {
    // 停止心跳
    stopHeartbeat();

    // 停止连接检查
    stopConnectionCheck();

    // 清理WebSocket连接
    if (socket) {
        // 移除所有事件监听器
        if (socket.onopen) socket.onopen = null;
        if (socket.onmessage) socket.onmessage = null;
        if (socket.onclose) socket.onclose = null;
        if (socket.onerror) socket.onerror = null;

        // 如果连接仍然打开，关闭它
        if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
            try {
                socket.close();
            } catch (err) {
                console.error('关闭WebSocket连接时出错:', err);
            }
        }
    }

    // 重置状态变量
    pendingConnection = false;
    isClosing = false;
}

/**
 * 更新连接状态
 * @param {string} state - 连接状态: 'connecting', 'connected', 'closing', 'closed', 'error'
 * @param {Object} [data] - 附加数据
 */
function updateConnectionState(state, data = {}) {
    const prevState = {
        isClosing,
        pendingConnection
    };

    // 更新状态
    switch (state) {
        case 'connecting':
            pendingConnection = true;
            isClosing = false;
            break;
        case 'connected':
            pendingConnection = false;
            isClosing = false;
            break;
        case 'closing':
            isClosing = true;
            break;
        case 'closed':
        case 'error':
            pendingConnection = false;
            isClosing = false;
            break;
        default:
            console.error('未知的连接状态:', state);
            return;
    }

    // 记录状态变化
    if (window.DEBUG_MODE || state === 'error') {
        console.log(`WebSocket连接状态变化: ${JSON.stringify(prevState)} -> ${state}`, data);
    }

    // 触发状态变化事件
    document.dispatchEvent(new CustomEvent('ws-state-change', {
        detail: {
            state,
            prevState,
            data
        }
    }));
}

/**
 * 处理WebSocket消息
 * @param {MessageEvent} event - WebSocket消息事件
 */
function handleWebSocketMessage(event) {
    try {
        // 尝试解析JSON
        let data;
        try {
            data = JSON.parse(event.data);
        } catch (jsonError) {
            console.error("JSON解析错误:", jsonError, "原始数据:", event.data);
            // 触发消息解析错误事件
            document.dispatchEvent(new CustomEvent('ws-message-parse-error', {
                detail: {
                    error: jsonError.message,
                    rawData: event.data.substring(0, 200) + (event.data.length > 200 ? '...' : '') // 限制长度，避免日志过大
                }
            }));
            return;
        }

        const type = data.type;

        // 处理心跳响应，不记录日志避免日志过多
        if (type === 'heartbeat_response') {
            // 心跳响应，连接正常
            // 触发心跳响应事件，更新最后响应时间，传递详细信息
            window.dispatchEvent(new CustomEvent('heartbeat_response', {
                detail: {
                    clientTimestamp: data.client_timestamp || data.timestamp,
                    serverTimestamp: data.server_timestamp,
                    timestamp: data.timestamp
                }
            }));

            // 只在调试模式下打印心跳响应日志
            if (window.DEBUG_MODE) {
                console.log("收到心跳响应");
            }
            return;
        }

        // 检查消息类型是否存在
        if (!type) {
            console.warn("收到无类型消息:", data);
            return;
        }

        console.log("收到消息类型:", type);

        // 触发自定义事件，便于其他模块监听
        document.dispatchEvent(new CustomEvent('ws-message', { detail: data }));

        // 如果有对应的处理器，调用它
        if (messageHandlers[type]) {
            try {
                messageHandlers[type](data);
            } catch (handlerError) {
                console.error(`处理消息类型 "${type}" 时出错:`, handlerError);
                // 触发消息处理错误事件
                document.dispatchEvent(new CustomEvent('ws-message-handler-error', {
                    detail: {
                        type,
                        error: handlerError.message,
                        data
                    }
                }));
            }
        } else {
            console.log("未处理的消息类型:", type, data);
        }
    } catch (error) {
        console.error("处理WebSocket消息错误:", error);
    }
}

/**
 * 发送WebSocket消息
 * @param {Object} message - 要发送的消息对象
 * @returns {boolean} 是否成功发送
 */
export function sendMessage(message) {
    // 如果是心跳消息，不显示警告
    const isHeartbeat = message.type === 'heartbeat';

    if (!socket || socket.readyState !== WebSocket.OPEN) {
        if (!isHeartbeat) {
            console.error("WebSocket未连接，无法发送消息");

            // 将消息保存到离线队列
            addOfflineMessage(message)
                .then(id => {
                    if (id !== -1) {
                        console.log(`消息已保存到离线队列，ID: ${id}`);
                    }
                })
                .catch(err => {
                    console.error('保存离线消息失败:', err);
                });

            // 获取当前连接状态
            const currentState = pendingConnection ? 'connecting' : 'closed';

            // 如果不是正在连接中，才显示提示并尝试重连
            if (currentState === 'closed') {
                alert("连接已断开，消息将在重连后发送");

                // 如果用户已登录，尝试重新连接
                if (userId) {
                    // 创建新的Promise来处理重连
                    new Promise((resolve, reject) => {
                        pendingConnection = false; // 重置连接状态
                        createWebSocketConnection(resolve, reject);
                    }).catch(err => {
                        console.error('重连失败:', err);
                    });
                }
            } else {
                console.log("连接正在进行中，消息已保存到离线队列");
            }
        }
        return false;
    }

    try {
        // 检查消息大小
        const messageStr = JSON.stringify(message);
        const messageSize = new Blob([messageStr]).size;

        // 根据消息类型设置不同的大小限制
        let maxAllowedSize;
        const messageType = message.type || 'unknown';

        // 定义不同类型消息的大小限制
        const SIZE_LIMITS = {
            // 心跳消息 - 很小，不需要限制
            'heartbeat': 1024, // 1KB

            // 普通文本消息 - 较小
            'private_message': 100 * 1024, // 100KB
            'group_message': 100 * 1024, // 100KB

            // 包含图片的消息 - 较大
            'image_message': 5 * 1024 * 1024, // 5MB

            // 包含文件的消息 - 最大
            'file_message': 10 * 1024 * 1024, // 10MB

            // 其他消息类型 - 默认限制
            'default': 2 * 1024 * 1024 // 2MB
        };

        // 获取当前消息类型的大小限制，如果没有特定限制，使用默认限制
        maxAllowedSize = SIZE_LIMITS[messageType] || SIZE_LIMITS.default;

        // 特殊处理：如果消息包含is_html标记且为true，可能包含富文本内容，增加限制
        if (message.is_html) {
            maxAllowedSize = Math.max(maxAllowedSize, 500 * 1024); // 至少500KB
        }

        // 特殊处理：如果消息类型是私聊或群聊，但包含特殊消息类型（如通话信号），使用更小的限制
        if ((messageType === 'private_message' || messageType === 'group_message') &&
            message.message_type && message.message_type.includes('call')) {
            maxAllowedSize = 10 * 1024; // 通话信号消息限制为10KB
        }

        // 检查消息是否超过限制
        if (messageSize > maxAllowedSize && !isHeartbeat) {
            console.error(`消息过大 (${Math.round(messageSize / 1024)}KB > ${Math.round(maxAllowedSize / 1024)}KB), 类型: ${messageType}`);

            // 根据消息类型提供不同的提示
            let errorMessage;
            if (messageType.includes('image')) {
                errorMessage = '图片内容过大，请压缩图片后重试';
            } else if (messageType.includes('file')) {
                errorMessage = '文件过大，请分享链接或使用云存储服务';
            } else {
                errorMessage = '消息内容过大，请减小内容或分多次发送';
            }

            alert(errorMessage);
            return false;
        }

        if (!isHeartbeat) {
            console.log(`发送消息，类型: ${message.type}, 大小: ${Math.round(messageSize / 1024)}KB`);
        }

        socket.send(messageStr);
        return true;
    } catch (error) {
        if (!isHeartbeat) {
            console.error("发送消息错误:", error);

            // 将消息保存到离线队列
            addOfflineMessage(message)
                .then(id => {
                    if (id !== -1) {
                        console.log(`发送失败的消息已保存到离线队列，ID: ${id}`);
                    }
                })
                .catch(err => {
                    console.error('保存离线消息失败:', err);
                });

            // 更新连接状态为错误
            updateConnectionState('error', {
                reason: 'message_send_failed',
                messageType: message.type,
                error
            });

            // 发送失败可能是连接已断开但未触发onclose
            // 使用资源清理函数确保彻底清理
            cleanupWebSocketResources();

            // 触发消息发送失败事件
            document.dispatchEvent(new CustomEvent('ws-message-send-failed', {
                detail: {
                    message,
                    error: error.message || '发送失败',
                    timestamp: new Date().toISOString()
                }
            }));
        }
        return false;
    }
}

/**
 * 请求消息历史
 * @param {string} chatroomId - 聊天室ID
 * @param {number} offset - 偏移量
 * @param {number} limit - 限制数量
 * @returns {boolean} 是否成功发送请求
 */
export function requestMessageHistory(chatroomId, offset = 0, limit = 20) {
    return sendMessage({
        type: 'get_history',
        chatroom_id: chatroomId,
        offset: offset,
        limit: limit
    });
}

/**
 * 发送聊天消息
 * @param {Object} currentChatroom - 当前聊天室对象
 * @param {string} content - 消息内容
 * @param {string} senderId - 发送者ID
 * @param {boolean} [isHtml=false] - 是否为HTML内容
 * @param {string} [messageType=''] - 消息类型，用于特殊消息
 * @param {string} [contentType='text'] - 内容类型: 'text', 'image', 'file'
 * @returns {boolean} 是否成功发送
 */
export function sendChatMessage(currentChatroom, content, senderId, isHtml = false, messageType = '', contentType = 'text') {
    if (!content || !currentChatroom) return false;

    // 根据内容类型确定消息类型
    let type;
    if (currentChatroom.is_group) {
        type = 'group_message';
    } else {
        type = 'private_message';
    }
    // 如果是图片或文件，修改消息类型
    if (contentType === 'image') {
        type = 'image_message';
    } else if (contentType === 'file') {
        type = 'file_message';
    }

    const msgData = {
        type: type,
        msg: content,
        sender: senderId,
        is_html: isHtml, // 添加标记来区分普通文本和HTML内容
        content_type: contentType // 添加内容类型标记
    };

    // 添加消息类型，用于特殊消息（如通话信号）
    if (messageType) {
        msgData.message_type = messageType;
    }

    if (currentChatroom.is_group) {
        msgData.chatroom_id = currentChatroom.id;
    } else {
        let sjz = currentChatroom.members.filter(item  => item .user_id !== senderId.toString());
        // 私聊，找出对方用户
        msgData.recipient = sjz[0].user_id;
        msgData.chatroom_id = sjz[0].chatroom_id;
    }

    // 添加时间戳
    msgData.timestamp = Date.now();

    return sendMessage(msgData);
}

/**
 * 编辑消息
 * @param {string} messageId - 消息ID
 * @param {string} newContent - 新消息内容
 * @param {string} senderId - 发送者ID
 * @returns {boolean} 是否成功发送
 */
export function editChatMessage(messageId, newContent, senderId) {
    return sendMessage({
        type: 'edit_message',
        message_id: messageId,
        content: newContent,
        sender: senderId
    });
}

/**
 * 创建群组
 * @param {string} name - 群组名称
 * @param {Array} members - 成员ID数组
 * @param {string} senderId - 创建者ID
 * @returns {boolean} 是否成功发送
 */
export function createGroup(name, members, senderId) {
    return sendMessage({
        type: 'create_group',
        name: name,
        sender: senderId,
        members: members
    });
}

/**
 * 添加用户到群组
 * @param {string} userId - 用户ID
 * @param {string} groupId - 群组ID
 * @param {string} senderId - 发送者ID
 * @returns {boolean} 是否成功发送
 */
export function addUserToGroup(userId, groupId, senderId) {
    return sendMessage({
        type: 'add_to_group',
        user_id: userId,
        group_id: groupId,
        sender: senderId
    });
}