<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .connecting { background-color: #fff3cd; color: #856404; }
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .stats-table th, .stats-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .stats-table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接调试工具</h1>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button onclick="getInfo()">获取连接信息</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="info-box">
            <h3>连接信息</h3>
            <div id="connectionInfo">点击"获取连接信息"按钮查看详细信息</div>
        </div>
        
        <div class="info-box">
            <h3>连接统计</h3>
            <table class="stats-table" id="statsTable">
                <tr><th>项目</th><th>值</th></tr>
                <tr><td>总连接次数</td><td id="totalConnections">0</td></tr>
                <tr><td>总断开次数</td><td id="totalDisconnections">0</td></tr>
                <tr><td>最后连接时间</td><td id="lastConnectionTime">-</td></tr>
                <tr><td>最后断开时间</td><td id="lastDisconnectionTime">-</td></tr>
            </table>
        </div>
        
        <div class="info-box">
            <h3>断开原因统计</h3>
            <div id="disconnectionReasons">暂无数据</div>
        </div>
        
        <div class="info-box">
            <h3>实时日志</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script type="module">
        import { connectWebSocket, disconnectWebSocket, getConnectionInfo } from './websocket.js';
        
        let socket = null;
        
        // 将函数暴露到全局作用域
        window.connect = connect;
        window.disconnect = disconnect;
        window.getInfo = getInfo;
        window.clearLog = clearLog;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = `状态: ${status}`;
            statusDiv.className = `status ${className}`;
        }
        
        function updateButtons(connected) {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
        }
        
        async function connect() {
            try {
                updateStatus('连接中...', 'connecting');
                updateButtons(false);
                log('开始连接WebSocket...');
                
                // 使用测试用户ID
                const userId = '249753';
                
                socket = await connectWebSocket(userId, () => {
                    log('WebSocket连接成功回调触发');
                    updateStatus('已连接', 'connected');
                    updateButtons(true);
                }, {
                    system: (data) => {
                        log(`收到系统消息: ${data.msg}`);
                    },
                    chatroom_list: (data) => {
                        log(`收到聊天室列表: ${data.chatrooms ? Object.keys(data.chatrooms).length : 0} 个聊天室`);
                    },
                    unread_counts: (data) => {
                        log(`收到未读消息计数: ${JSON.stringify(data.counts)}`);
                    },
                    heartbeat_response: (data) => {
                        log(`收到心跳响应: ${data.server_timestamp}`);
                    }
                });
                
                log('WebSocket连接建立成功');
                
            } catch (error) {
                log(`连接失败: ${error.message}`);
                updateStatus('连接失败', 'disconnected');
                updateButtons(false);
            }
        }
        
        function disconnect() {
            if (socket) {
                log('主动断开WebSocket连接...');
                disconnectWebSocket();
                socket = null;
                updateStatus('已断开', 'disconnected');
                updateButtons(false);
                log('WebSocket连接已断开');
            }
        }
        
        function getInfo() {
            const info = getConnectionInfo();
            document.getElementById('connectionInfo').innerHTML = `
                <pre>${JSON.stringify(info, null, 2)}</pre>
            `;
            
            // 更新统计表格
            if (info.stats) {
                document.getElementById('totalConnections').textContent = info.stats.totalConnections;
                document.getElementById('totalDisconnections').textContent = info.stats.totalDisconnections;
                document.getElementById('lastConnectionTime').textContent = info.stats.lastConnectionTime || '-';
                document.getElementById('lastDisconnectionTime').textContent = info.stats.lastDisconnectionTime || '-';
                
                // 更新断开原因统计
                const reasons = info.stats.disconnectionReasons;
                if (Object.keys(reasons).length > 0) {
                    let reasonsHtml = '<ul>';
                    for (const [code, count] of Object.entries(reasons)) {
                        reasonsHtml += `<li>错误码 ${code}: ${count} 次</li>`;
                    }
                    reasonsHtml += '</ul>';
                    document.getElementById('disconnectionReasons').innerHTML = reasonsHtml;
                } else {
                    document.getElementById('disconnectionReasons').textContent = '暂无数据';
                }
            }
            
            log('连接信息已更新');
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 监听WebSocket事件
        document.addEventListener('ws-connection-error', (event) => {
            log(`连接错误事件: ${event.detail.message}`);
            updateStatus('连接错误', 'disconnected');
            updateButtons(false);
        });
        
        document.addEventListener('ws-heartbeat-timeout', (event) => {
            log(`心跳超时事件: ${JSON.stringify(event.detail)}`);
        });
        
        document.addEventListener('ws-message-send-failed', (event) => {
            log(`消息发送失败事件: ${JSON.stringify(event.detail)}`);
        });
        
        // 页面加载完成后的初始化
        log('WebSocket调试工具已加载');
        log('点击"连接"按钮开始测试');
        
        // 定期更新连接信息
        setInterval(() => {
            if (socket) {
                getInfo();
            }
        }, 5000);
    </script>
</body>
</html>
